# Draft Preview Implementation Plan

## Overview

This comprehensive plan outlines the implementation of PayloadCMS draft preview functionality for the Articles collection in Börsen Blick. This feature enables content editors to securely preview draft articles before publishing, significantly improving the editorial workflow and content quality assurance.

**Implementation Scope:** Draft preview only (not live preview) following official PayloadCMS and Next.js App Router patterns.

## Architecture Overview

The draft preview system consists of three main components:

1. **PayloadCMS Admin Integration** - Preview button and URL generation in the admin panel
2. **Next.js API Routes** - Secure preview authentication and draft mode activation
3. **Frontend Draft Rendering** - Conditional content display with visual draft indicators

```mermaid
graph TB
    A[PayloadCMS Admin] -->|Click Preview Button| B[Preview URL with Secret]
    B -->|GET /api/preview| C[Preview API Route]
    C -->|Validate Secret| D[Enable Draft Mode]
    D -->|Redirect| E[Article Page with Draft]
    E -->|Draft Mode Active| F[Fetch Draft Content]
    F -->|Display| G[Article with Draft Banner]
```

## Current State Analysis

### Articles Collection

- Located at `src/collections/Articles.ts`
- Uses workflow stages: `candidate-article`, `translated`, `ready-for-review`, `published`
- Currently only published articles are visible on frontend
- Has comprehensive metadata and German/English translation tabs

### Frontend Structure

- Article pages at `src/app/(frontend)/articles/[slug]/page.tsx`
- Uses App Router with static generation for published articles
- Current filtering: `article.workflowStage !== 'published'` results in 404

### PayloadCMS Configuration

- Config at `src/payload.config.ts`
- No existing preview configuration
- Uses PostgreSQL adapter with SEO plugin

## Simplified Implementation Plan

### Phase 1: Articles Collection Updates

#### 1.1 Add Preview Configuration to Articles Collection

**File:** `src/collections/Articles.ts`

Add preview configuration following PayloadCMS patterns:

```typescript
export const Articles: CollectionConfig = {
  slug: 'articles',
  // ... existing config
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'workflowStage', 'articleType', 'updatedAt'],
    group: 'Content',
    // Add preview configuration using PayloadCMS pattern
    preview: ({ slug, collection }) => {
      if (!slug) return null;

      const params = new URLSearchParams({
        slug,
        collection: 'articles',
        path: `/articles/${slug}`,
        previewSecret: process.env.PAYLOAD_PUBLIC_DRAFT_SECRET || '',
      });

      return `/api/preview?${params.toString()}`;
    },
  },
  // ... existing fields and config
  versions: {
    drafts: true, // Enable drafts
  },
};
```

#### 1.2 Environment Variables

Add to `.env.local` (following PayloadCMS example naming):

```env
NEXT_PUBLIC_SERVER_URL=http://localhost:3000
PAYLOAD_PUBLIC_DRAFT_SECRET=your-secure-preview-secret-here
```

**Note:** The `PAYLOAD_PUBLIC_DRAFT_SECRET` should be a secure, random string used to authenticate preview requests.

### Phase 2: API Routes for Preview

#### 2.1 Preview API Route

**File:** `src/app/api/preview/route.ts`

```typescript
import { draftMode } from 'next/headers';
import { redirect } from 'next/navigation';

export async function GET(request: Request): Promise<Response> {
  const { searchParams } = new URL(request.url);
  const previewSecret = searchParams.get('previewSecret');
  const slug = searchParams.get('slug');
  const collection = searchParams.get('collection');
  const path = searchParams.get('path');

  // Check the secret and required parameters
  if (
    previewSecret !== process.env.PAYLOAD_PUBLIC_DRAFT_SECRET ||
    !slug ||
    collection !== 'articles'
  ) {
    return new Response('Invalid token', { status: 401 });
  }

  // Enable Draft Mode
  const draft = await draftMode();
  draft.enable();

  // Redirect to the article page using the path parameter
  redirect(path || `/articles/${slug}`);
}
```

#### 2.2 Disable Draft Mode Route

**File:** `src/app/api/disable-draft/route.ts`

```typescript
import { draftMode } from 'next/headers';
import { redirect } from 'next/navigation';

export async function GET(): Promise<Response> {
  const draft = await draftMode();
  draft.disable();

  redirect('/');
}
```

### Phase 3: Frontend Updates

#### 3.1 Complete Article Page Component Implementation

**File:** `src/app/(frontend)/articles/[slug]/page.tsx`

Comprehensive implementation following Next.js App Router and PayloadCMS patterns:

```typescript
import { draftMode } from 'next/headers';
import { getPayload } from 'payload';
import config from '@payload-config';

// Add this function near the top
async function getArticle(slug: string, isDraftMode: boolean) {
  if (isDraftMode) {
    // Fetch draft version when in draft mode
    const payload = await getPayload({ config });
    const articles = await payload.find({
      collection: 'articles',
      where: { slug: { equals: slug } },
      draft: true,
      limit: 1,
    });
    return articles.docs[0] || null;
  } else {
    // Use cached version for published articles
    const getCachedDoc = getCachedDocument('articles', slug, 2);
    const result = await getCachedDoc();
    return result as unknown as Article | null;
  }
}

// Update the main component
export default async function ArticlePage({ params }: ArticlePageProps) {
  const { slug } = await params;
  const draft = await draftMode();
  const isDraftMode = draft.isEnabled;

  const article = await getArticle(slug, isDraftMode);

  // Check access: draft mode allows any workflow stage, normal mode requires published
  if (!article || (!isDraftMode && article.workflowStage !== 'published')) {
    notFound();
  }

  return (
    <div className="min-h-dvh bg-background">
      {/* Draft Mode Banner */}
      {isDraftMode && (
        <div className="bg-yellow-50 border-b border-yellow-200 p-4 text-center">
          <p className="text-yellow-800 font-medium">
            Preview Mode - This is a draft version
            <a
              href="/api/disable-draft"
              className="ml-2 underline hover:no-underline"
            >
              Exit Preview
            </a>
          </p>
        </div>
      )}

      {/* Rest of existing component - no changes needed */}
      <ArticleAccessibilityNav />

      <div className="max-w-[1440px] mx-auto px-4 py-8 lg:py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-0">
          {/* Existing JSX structure */}
          <aside
            id="article-metadata"
            className="sm:border-r-2 sm:border-gray-50 sm:pr-2 lg:pr-3 xl:pr-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-3 sm:order-1"
            aria-labelledby="article-details-heading"
            tabIndex={-1}
          >
            <h2 id="article-details-heading" className="sr-only">
              Article Details
            </h2>
            <div className="flex flex-col gap-6">
              <ArticleHeader article={article} locale="de" />
              <ArticleMetadata article={article} />
            </div>
          </aside>

          <section
            id="article-content"
            className="sm:col-span-1 lg:col-span-2 xl:col-span-2 sm:border-r-2 sm:border-gray-50 sm:px-2 lg:px-3 xl:px-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-1 sm:order-2"
            aria-labelledby="article-content-heading"
            tabIndex={-1}
          >
            <h2 id="article-content-heading" className="sr-only">
              Article Content
            </h2>
            <ArticleContent article={article} locale="de" />
          </section>

          <aside
            id="related-articles"
            className="xl:pl-4 order-2 sm:order-3"
            aria-labelledby="related-articles-heading"
            tabIndex={-1}
          >
            <h2 id="related-articles-heading" className="sr-only">
              Related Articles
            </h2>
            <Suspense fallback={<RelatedArticlesSkeleton maxArticles={4} />}>
              <RelatedArticlesSection
                currentArticle={article}
                maxArticles={4}
                locale="de"
              />
            </Suspense>
          </aside>
        </div>
      </div>
    </div>
  );
}
```

#### 3.2 Update Static Generation

Make the page handle dynamic draft routes:

```typescript
// Add this to the page component
export const dynamicParams = true;

// Keep existing generateStaticParams for published articles
export async function generateStaticParams(): Promise<{ slug: string }[]> {
  try {
    // Only generate static params for published articles
    return await getCachedPublishedArticleSlugs();
  } catch (error) {
    console.error('Error generating static params:', error);
    return [];
  }
}
```

#### 3.2 Enhanced Error Handling and Security

**File:** `src/app/(frontend)/articles/[slug]/error.tsx`

```typescript
'use client';

import { useEffect } from 'react';

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function ArticleError({ error, reset }: ErrorProps) {
  useEffect(() => {
    // Log error to monitoring service
    console.error('Article page error:', error);
  }, [error]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Something went wrong!
        </h2>
        <p className="text-gray-600 mb-6">
          We encountered an error while loading this article. This could be due to:
        </p>
        <ul className="text-sm text-gray-500 mb-6 space-y-1">
          <li>• The article may have been moved or deleted</li>
          <li>• Network connectivity issues</li>
          <li>• Temporary server problems</li>
        </ul>
        <div className="flex gap-3">
          <button
            onClick={reset}
            className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
          <a
            href="/"
            className="flex-1 text-center bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors"
          >
            Go Home
          </a>
        </div>
      </div>
    </div>
  );
}
```

#### 3.3 Draft Mode Security Middleware

**File:** `src/middleware.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname, searchParams } = request.nextUrl;

  // Security check for preview API routes
  if (pathname.startsWith('/api/preview')) {
    const previewSecret = searchParams.get('previewSecret');

    // Rate limiting for preview requests (simple implementation)
    const clientIP = request.ip || 'unknown';
    const rateLimitKey = `preview_${clientIP}`;

    // Add rate limiting logic here if needed
    // This is a basic example - use Redis or similar for production

    // Log preview access attempts for security monitoring
    console.log(
      `Preview access attempt from ${clientIP} at ${new Date().toISOString()}`
    );

    // Additional security headers for preview routes
    const response = NextResponse.next();
    response.headers.set('X-Robots-Tag', 'noindex, nofollow');
    response.headers.set(
      'Cache-Control',
      'no-cache, no-store, must-revalidate'
    );

    return response;
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/api/preview/:path*', '/api/disable-draft/:path*'],
};
```

## Implementation Timeline

### Week 1: Foundation & Core Implementation

**Days 1-2: PayloadCMS Configuration**

- [ ] Phase 1: Articles collection updates and environment setup
- [ ] Configure preview URLs and draft versioning
- [ ] Set up secure environment variables
- [ ] Test admin preview button functionality

**Days 3-4: API Routes & Security**

- [ ] Phase 2: Create preview and disable-draft API routes
- [ ] Implement security middleware and rate limiting
- [ ] Add comprehensive error handling
- [ ] Test API route security and functionality

**Days 5-7: Frontend Integration**

- [ ] Phase 3: Complete frontend component updates
- [ ] Implement draft mode indicators and banners
- [ ] Add proper metadata handling for drafts
- [ ] Create error boundaries and loading states
- [ ] Integration testing of complete workflow

## Dependencies & Requirements

### Technical Requirements

- PayloadCMS 3.x with draft functionality
- Next.js App Router with draft mode support
- Environment variable management

### Configuration Changes

- Update Articles collection with preview config
- Add new API routes for draft handling
- Modify frontend article page for draft display
- Add environment variables for preview URLs

### Environment Variables

```env
NEXT_PUBLIC_SERVER_URL=http://localhost:3000
PAYLOAD_PUBLIC_DRAFT_SECRET=your-secure-preview-secret-here
```

**Security Note:** Generate a strong random string for `PAYLOAD_PUBLIC_DRAFT_SECRET` (e.g., `openssl rand -hex 32`)

## Success Criteria

1. ✅ Content editors can preview draft articles via preview button in admin
2. ✅ Draft preview works for all workflow stages (candidate-article, translated, ready-for-review)
3. ✅ Published articles maintain current performance and caching
4. ✅ Clear visual indication when in preview mode
5. ✅ Easy exit from preview mode
6. ✅ Secure preview access with secret validation

## Comprehensive Testing & Quality Assurance

### 🧪 Automated Testing Strategy

**Unit Tests** - `src/__tests__/draft-preview/`

```typescript
// Example test structure following Vitest patterns
describe('Draft Preview System', () => {
  describe('getArticle function', () => {
    it('fetches published articles when not in draft mode', async () => {
      // Mock implementation following Next.js patterns
    });

    it('fetches draft content when in draft mode', async () => {
      // Test draft-specific logic
    });
  });

  describe('Preview API Routes', () => {
    it('validates preview secrets correctly', async () => {
      // Security validation tests
    });
  });
});
```

**Integration Tests** - PayloadCMS + Next.js App Router

```bash
# Test admin preview button generation
pnpm test:integration

# Test API route functionality
curl -X GET "http://localhost:3000/api/preview?slug=test&collection=articles&previewSecret=secret"

# Verify draft mode activation
curl -X GET "http://localhost:3000/articles/test" -H "Cookie: __prerender_bypass=token"
```

### 🔐 Security Testing Protocol

**Authentication & Access Control**

- [ ] ✅ Invalid preview secrets return 401 Unauthorized
- [ ] ✅ Missing parameters return 400 Bad Request
- [ ] ✅ Preview URLs require valid previewSecret
- [ ] ✅ Draft content inaccessible without authentication
- [ ] ✅ Rate limiting prevents preview endpoint abuse
- [ ] ✅ Admin authentication required for preview button access

**Content Security**

- [ ] ✅ Draft content not indexed (`robots: noindex, nofollow`)
- [ ] ✅ Draft metadata excludes production SEO tags
- [ ] ✅ Cache headers prevent draft content caching
- [ ] ✅ Error messages don't leak sensitive information
- [ ] ✅ Preview mode isolated from production analytics

### 🚀 Performance & Caching Validation

**Published Content Performance (Critical)**

- [ ] ✅ ISR caching maintains sub-200ms response times
- [ ] ✅ Static generation unaffected by draft implementation
- [ ] ✅ CDN caching works correctly for published articles
- [ ] ✅ Database queries optimized (< 50ms for cached content)

**Draft Mode Performance**

- [ ] ✅ Draft queries complete within 500ms
- [ ] ✅ Multiple concurrent preview requests handled efficiently
- [ ] ✅ Draft mode doesn't impact published article cache
- [ ] ✅ Memory usage remains stable during preview sessions

### 🎯 Functional Testing Scenarios

**Editor Workflow Testing**

1. **Admin Interface**
   - [ ] Preview button visible for all workflow stages
   - [ ] Button generates correct URLs with proper parameters
   - [ ] Preview opens in new tab with draft banner

2. **Content Display**
   - [ ] All workflow stages render correctly:
     - [ ] `candidate-article` - Shows extraction stage
     - [ ] `translated` - Shows translation complete
     - [ ] `ready-for-review` - Shows review stage
     - [ ] Draft status indicators display accurately

3. **Navigation & UX**
   - [ ] Exit preview redirects to homepage
   - [ ] Draft banner shows current workflow stage
   - [ ] Error states display helpful messages

**Edge Case Testing**

- [ ] ✅ Deleted draft articles return proper 404
- [ ] ✅ Network failures show graceful error boundaries
- [ ] ✅ Malformed preview URLs handled securely
- [ ] ✅ Concurrent edits don't break preview state
- [ ] ✅ Session expiration handled gracefully

### 🔧 End-to-End Testing (Playwright)

```typescript
// Complete workflow validation
test('complete draft preview workflow', async ({ page, context }) => {
  // 1. Admin login
  await page.goto('/admin');
  await adminLogin(page);

  // 2. Navigate to article
  await page.goto('/admin/collections/articles/create');
  await fillArticleForm(page);

  // 3. Test preview functionality
  const [previewPage] = await Promise.all([
    context.waitForEvent('page'),
    page.click('[data-testid=preview-button]'),
  ]);

  // 4. Verify draft mode
  await expect(previewPage.locator('[data-testid=draft-banner]')).toBeVisible();
  await expect(previewPage.locator('text=candidate-article')).toBeVisible();

  // 5. Test exit preview
  await previewPage.click('text=Exit Preview');
  await expect(previewPage).toHaveURL('/');
});
```

### 📊 Quality Gates & Success Metrics

**Pre-Deployment Checklist**

- [ ] ✅ All automated tests passing (Unit + Integration)
- [ ] ✅ Security scan completed with no high-severity issues
- [ ] ✅ Performance benchmarks met (see above thresholds)
- [ ] ✅ Manual testing completed across all browsers
- [ ] ✅ Error handling verified in production-like environment

**Success Criteria Validation**

- [ ] ✅ Content editors can preview all workflow stages
- [ ] ✅ Preview URLs work reliably across team members
- [ ] ✅ Published article performance unchanged (< 5% regression)
- [ ] ✅ Zero unauthorized access to draft content
- [ ] ✅ Clear visual indication of draft mode active
- [ ] ✅ Easy exit from preview mode

**Monitoring & Observability**

- [ ] Preview request logging for security audit
- [ ] Error tracking for failed preview attempts
- [ ] Performance monitoring for draft queries
- [ ] Usage analytics for preview feature adoption

---

## Next Steps & Implementation Roadmap

### 📋 Implementation Checklist

**Phase 1 - Foundation (Days 1-2)**

- [ ] Update Articles collection with preview configuration
- [ ] Configure environment variables and secrets
- [ ] Test PayloadCMS admin preview button

**Phase 2 - API & Security (Days 3-4)**

- [ ] Implement preview and disable-draft API routes
- [ ] Add security middleware and headers
- [ ] Implement comprehensive error handling
- [ ] Security testing and validation

**Phase 3 - Frontend (Days 5-7)**

- [ ] Update article page component with draft support
- [ ] Implement draft mode banners and indicators
- [ ] Add error boundaries and loading states
- [ ] Complete end-to-end testing

### 🚦 Go-Live Criteria

**Technical Requirements**
✅ All automated tests passing  
✅ Security review completed  
✅ Performance benchmarks met  
✅ Error handling thoroughly tested  
✅ Documentation completed

**Business Requirements**  
✅ Editor training completed  
✅ Preview workflow validated by content team  
✅ Rollback plan documented and tested  
✅ Support documentation available

**Final Validation**

1. Conduct user acceptance testing with content editors
2. Perform load testing on preview endpoints
3. Validate security controls in production-like environment
4. Complete accessibility audit of draft mode UI
5. Document troubleshooting procedures for support team

---

> **Ready for Implementation** - This comprehensive plan follows Next.js App Router best practices, PayloadCMS patterns, and includes robust testing, security, and quality assurance measures.
