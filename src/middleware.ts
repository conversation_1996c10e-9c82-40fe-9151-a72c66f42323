import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname, searchParams } = request.nextUrl;

  // Security check for preview API routes
  if (pathname.startsWith('/api/preview')) {
    const previewSecret = searchParams.get('previewSecret');

    // Rate limiting for preview requests (simple implementation)
    const clientIP =
      request.headers.get('x-forwarded-for') ||
      request.headers.get('x-real-ip') ||
      'unknown';
    const rateLimitKey = `preview_${clientIP}`;

    // Add rate limiting logic here if needed
    // This is a basic example - use Redis or similar for production

    // Log preview access attempts for security monitoring
    console.log(
      `Preview access attempt from ${clientIP} at ${new Date().toISOString()}`
    );

    // Additional security headers for preview routes
    const response = NextResponse.next();
    response.headers.set('X-Robots-Tag', 'noindex, nofollow');
    response.headers.set(
      'Cache-Control',
      'no-cache, no-store, must-revalidate'
    );

    return response;
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/api/preview/:path*', '/api/disable-draft/:path*'],
};
