import { NextResponse } from 'next/server';
import { revalidateTag, revalidatePath } from 'next/cache';

export async function POST() {
  try {
    console.log('🧹 Manual cache clearing initiated...');

    // Clear homepage cache
    revalidatePath('/');

    // Clear all article caches
    revalidateTag('articles');

    // Clear specific tier caches
    revalidateTag('tier-1');
    revalidateTag('tier-2');
    revalidateTag('tier-3');

    // Clear additional cache tags
    revalidateTag('tier-1-articles');
    revalidateTag('tier-2-articles');
    revalidateTag('tier-3-articles');
    revalidateTag('all-tier-articles');

    console.log('✅ Cache cleared successfully');

    return NextResponse.json({
      success: true,
      message: 'Homepage cache cleared successfully',
      clearedCaches: [
        'Homepage path (/)',
        'Article caches (articles)',
        'Tier caches (tier-1, tier-2, tier-3)',
        'Tier-specific article caches',
      ],
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Failed to clear cache:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to clear cache',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Cache clearing endpoint',
    usage: 'Send a POST request to this endpoint to clear the homepage cache',
    clearedCaches: [
      'Homepage path (/)',
      'Article caches (articles)',
      'Tier caches (tier-1, tier-2, tier-3)',
      'Tier-specific article caches',
    ],
  });
}
