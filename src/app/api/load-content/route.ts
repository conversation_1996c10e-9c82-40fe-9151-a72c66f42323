import config from '@payload-config';
import { NextResponse } from 'next/server';
import { getPayload } from 'payload';

/**
 * Content Loading API Endpoint
 *
 * Populates essential collections: RSS feeds, Keywords, Categories
 * Safe to run multiple times (idempotent)
 *
 * GET: Returns current database state
 * POST: Loads sample content into database
 */

export async function POST() {
  const startTime = Date.now();

  try {
    console.log('🚀 Loading essential content into database...');

    const payload = await getPayload({ config });

    let keywordsCreated = 0;
    let categoriesCreated = 0;
    let feedsCreated = 0;

    // Step 1: Create Keywords Collection
    console.log('📝 Creating Keywords...');

    const keywords = [
      { keyword: 'Krypto', englishKeyword: 'Crypto', isActive: true },
      { keyword: 'Gold', englishKeyword: 'Gold', isActive: true },
      { keyword: 'Mineralien', englishKeyword: 'Minerals', isActive: true },
      { keyword: 'Technologie', englishKeyword: 'Technology', isActive: true },
      { keyword: 'Energie', englishKeyword: 'Energy', isActive: true },
      { keyword: 'DAX', englishKeyword: 'DAX', isActive: true },
      { keyword: 'Rohstoffe', englishKeyword: 'Commodities', isActive: true },
      { keyword: 'Wirtschaft', englishKeyword: 'Economy', isActive: true },
      { keyword: 'Aktienmarkt', englishKeyword: 'StockMarket', isActive: true },
      { keyword: 'Währungen', englishKeyword: 'Currencies', isActive: true },
      { keyword: 'Aktien', englishKeyword: 'Stocks', isActive: true },
      { keyword: 'Börsengang', englishKeyword: 'IPO', isActive: true },
      { keyword: 'Fusion', englishKeyword: 'Merger', isActive: true },
      { keyword: 'Nebenwerte', englishKeyword: 'Small Cap', isActive: true },
      {
        keyword: 'Wachstumsaktien',
        englishKeyword: 'Growth Stocks',
        isActive: true,
      },
      { keyword: 'Dividende', englishKeyword: 'Dividend', isActive: true },
      { keyword: 'Wall Street', englishKeyword: 'Wall Street', isActive: true },
      { keyword: 'Prognose', englishKeyword: 'Forecast', isActive: true },
      { keyword: 'Investition', englishKeyword: 'Investment', isActive: true },
      { keyword: 'ETF', englishKeyword: 'ETF', isActive: true },
    ];

    for (const keywordData of keywords) {
      try {
        const existing = await payload.find({
          collection: 'keywords',
          where: { keyword: { equals: keywordData.keyword } },
          limit: 1,
        });

        if (existing.docs.length === 0) {
          await payload.create({
            collection: 'keywords',
            data: keywordData,
          });
          keywordsCreated++;
          console.log(
            `✅ Created keyword: ${keywordData.keyword} (${keywordData.englishKeyword})`
          );
        } else {
          console.log(`⚠️ Keyword exists: ${keywordData.keyword}`);
        }
      } catch (error: any) {
        console.error(
          `❌ Failed to create keyword ${keywordData.keyword}:`,
          error.message
        );
      }
    }

    // Step 2: Create Categories Collection
    console.log('📂 Creating Categories...');

    const categories = [
      { title: 'Investitionen', english: 'Investment' },
      { title: 'Wirtschaft', english: 'Economics' },
      { title: 'Technologie', english: 'Technology' },
      { title: 'International', english: 'International' },
    ];

    for (const categoryData of categories) {
      try {
        const existing = await payload.find({
          collection: 'categories',
          where: { title: { equals: categoryData.title } },
          limit: 1,
        });

        if (existing.docs.length === 0) {
          await payload.create({
            collection: 'categories',
            data: categoryData,
          });
          categoriesCreated++;
          console.log(`✅ Created category: ${categoryData.title}`);
        } else {
          console.log(`⚠️ Category exists: ${categoryData.title}`);
        }
      } catch (error: any) {
        console.error(
          `❌ Failed to create category ${categoryData.title}:`,
          error.message
        );
      }
    }

    // Step 3: Create RSS Feeds Collection
    console.log('📡 Creating RSS Feeds...');

    // Common exclude tags for all feeds
    const commonExcludeTags = [
      { tag: '.advertisement' },
      { tag: '.ad-banner' },
      { tag: '.ad-container' },
      { tag: '.consent-banner' },
      { tag: '#usercentrics-root' },
      { tag: '.sp_message_container' },
      { tag: '.cookie-banner' },
      { tag: '.sidebar' },
      { tag: '.navigation' },
      { tag: '.nav' },
      { tag: '.header' },
      { tag: '.footer' },
      { tag: '.comments' },
      { tag: '.related-articles' },
      { tag: '.social-share' },
      { tag: '.newsletter' },
      { tag: '.subscription' },
      { tag: '.paywall' },
      { tag: '.carousel' },
      { tag: '.slider' },
      { tag: '.teaser' },
      { tag: '.recommendation' },
      { tag: '#ov-instrument-chart--full-screen' },
    ];

    const rssFeeds = [
      {
        name: 'Finanzen.net - News',
        url: 'https://www.finanzen.net/rss/news',
        isActive: true,
        language: 'de' as const,
        processingFrequency: 360, // 6 hours
        priority: 'high' as const,
        // Enhanced Firecrawl options with site-specific settings
        firecrawlOptions: {
          removeBase64Images: true,
          blockAds: true,
          excludeTags: [
            ...commonExcludeTags,
            { tag: '.breaking-news-container' },
            { tag: '.quickquotes' },
            { tag: '.ticker' },
            { tag: '.widget' },
            { tag: '.chart' },
            { tag: '.quote' },
            { tag: '.instrument' },
            { tag: '.performance' },
            { tag: 'table' },
            { tag: '.table' },
            { tag: '.data-table' },
            { tag: '.market-data' },
          ],
          includeTags: [],
        },
        // Processing options
        processingOptions: {
          maxFirecrawlScrape: 15, // Scrape up to 15 articles
          maxArticlesPerRun: 10, // Send up to 10 to OpenAI
          customTimeout: 50, // 50 seconds
          enableStealth: false,
          skipTranslation: false,
          skipEnhancement: false,
        },
        // Keyword filtering options
        keywordFiltering: {
          strictKeywordMatching: false,
          customKeywords: [],
        },
      },
      {
        name: 'Der Aktionär - News',
        url: 'https://www.deraktionaer.de/aktionaer-news.rss',
        isActive: true,
        language: 'de' as const,
        processingFrequency: 360, // 6 hours
        priority: 'high' as const,
        // Enhanced Firecrawl options with site-specific settings
        firecrawlOptions: {
          removeBase64Images: true,
          blockAds: true,
          excludeTags: [
            ...commonExcludeTags,
            { tag: '.breaking-news-container' },
            { tag: '.header-banner' },
            { tag: '.quickquotes' },
            { tag: '.ticker' },
            { tag: '.widget' },
            { tag: '.chart' },
            { tag: '.quote' },
            { tag: '.instrument' },
            { tag: '.performance' },
            { tag: '[class*="premium"]' },
            { tag: '[class*="abo"]' },
            { tag: '[class*="subscription"]' },
            { tag: '[class*="newsletter"]' },
          ],
          includeTags: [],
        },
        // Processing options
        processingOptions: {
          maxFirecrawlScrape: 15, // Scrape up to 15 articles
          maxArticlesPerRun: 10, // Send up to 10 to OpenAI
          customTimeout: 45, // 45 seconds
          enableStealth: false,
          skipTranslation: false,
          skipEnhancement: false,
        },
        // Keyword filtering options
        keywordFiltering: {
          strictKeywordMatching: false,
          customKeywords: [],
        },
      },
      {
        name: 'Onvista.de - News',
        url: 'https://www.onvista.de/news/feed/rss.xml',
        isActive: true,
        language: 'de' as const,
        processingFrequency: 360, // 6 hours
        priority: 'high' as const,
        // Enhanced Firecrawl options with site-specific settings
        firecrawlOptions: {
          removeBase64Images: true,
          blockAds: true,
          excludeTags: [
            ...commonExcludeTags,
            { tag: '.widget' },
            { tag: '.chart' },
            { tag: '.quote' },
            { tag: '.instrument' },
            { tag: '.performance' },
            { tag: 'table' },
            { tag: '.table' },
            { tag: '.data-table' },
            { tag: '.market-data' },
            { tag: '.stock-data' },
            { tag: '.price-data' },
            { tag: '.trading-data' },
            { tag: '.ticker' },
            { tag: '.tickerContainer' },
            { tag: '[class*="chart"]' },
            { tag: '[class*="ticker"]' },
            { tag: '[class*="quote"]' },
            { tag: '[class*="instrument"]' },
            { tag: '[class*="market"]' },
            { tag: '[class*="trading"]' },
            { tag: '[class*="performance"]' },
            { tag: '[class*="premium"]' },
            { tag: '[class*="werbung"]' },
            { tag: '[class*="broker"]' },
            { tag: '[class*="depot"]' },
          ],
          includeTags: [],
        },
        // Processing options
        processingOptions: {
          maxFirecrawlScrape: 15, // Scrape up to 15 articles
          maxArticlesPerRun: 10, // Send up to 10 to OpenAI
          customTimeout: 50, // 50 seconds
          enableStealth: false,
          skipTranslation: false,
          skipEnhancement: false,
        },
        // Keyword filtering options
        keywordFiltering: {
          strictKeywordMatching: false,
          customKeywords: [],
        },
      },
      {
        name: 'Finanznachrichten.de - Aktien',
        url: 'https://www.finanznachrichten.de/rss-aktien-nachrichten/',
        isActive: true,
        language: 'de' as const,
        processingFrequency: 360, // 6 hours
        priority: 'medium' as const,
        // Enhanced Firecrawl options with default settings
        firecrawlOptions: {
          removeBase64Images: true,
          blockAds: true,
          excludeTags: commonExcludeTags,
          includeTags: [],
        },
        // Processing options
        processingOptions: {
          maxFirecrawlScrape: 15, // Scrape up to 15 articles
          maxArticlesPerRun: 10, // Send up to 10 to OpenAI
          customTimeout: 30, // 30 seconds
          enableStealth: false,
          skipTranslation: false,
          skipEnhancement: false,
        },
        // Keyword filtering options
        keywordFiltering: {
          strictKeywordMatching: false,
          customKeywords: [],
        },
      },
      {
        name: 'Wallstreet Online - Nachrichten',
        url: 'https://www.wallstreet-online.de/rss/nachrichten-alle.xml',
        isActive: true,
        language: 'de' as const,
        processingFrequency: 360, // 6 hours
        priority: 'medium' as const,
        // Enhanced Firecrawl options with site-specific settings
        firecrawlOptions: {
          removeBase64Images: true,
          blockAds: true,
          excludeTags: [
            ...commonExcludeTags,
            { tag: '#quickquotesHead' },
            { tag: '#mooTickerContainer' },
            { tag: '#mooTickerContainerMobile' },
            { tag: '#dsky1' },
            { tag: '#dsky2' },
            { tag: '#headOut' },
            { tag: '#afterhead' },
            { tag: '#dban1' },
            { tag: '#breadcrumb' },
            { tag: '#kosocgen' },
            { tag: '.discussionCarousel' },
            { tag: '.reportsbox' },
            { tag: '.inlinetip' },
            { tag: '.boxContent' },
            { tag: '.emittentFooter' },
            { tag: '.boxFooter' },
            { tag: '.textTicker' },
            { tag: '.tickerContainer' },
          ],
          includeTags: [],
        },
        // Processing options
        processingOptions: {
          maxFirecrawlScrape: 18, // Scrape up to 18 articles
          maxArticlesPerRun: 12, // Send up to 12 to OpenAI
          customTimeout: 40, // 40 seconds
          enableStealth: false,
          skipTranslation: false,
          skipEnhancement: false,
        },
        // Keyword filtering options
        keywordFiltering: {
          strictKeywordMatching: false,
          customKeywords: [],
        },
      },
      {
        name: 'Deutsche Welle - Business (English)',
        url: 'https://rss.dw.com/rdf/rss-en-all',
        isActive: true,
        language: 'en' as const,
        processingFrequency: 720, // 12 hours
        priority: 'low' as const,
        // Enhanced Firecrawl options with site-specific settings for DW
        firecrawlOptions: {
          removeBase64Images: true,
          blockAds: true,
          excludeTags: [
            ...commonExcludeTags,
            { tag: '.live-blog' },
            { tag: '.liveblog' },
            { tag: '.live-updates' },
            { tag: '.live-ticker' },
            { tag: '.updates' },
            { tag: '.timeline' },
            { tag: '[id*="post-liveblog"]' },
            { tag: '[id*="liveblog-post"]' },
            { tag: '.live-post' },
            { tag: '.update-post' },
            { tag: '.news-update' },
            { tag: 'time' },
            { tag: '.timestamp' },
            { tag: '.date' },
            { tag: '.published' },
            { tag: '.updated' },
            { tag: '.embedded' },
            { tag: '.embed' },
            { tag: '.slideshow' },
            { tag: '.gallery' },
            { tag: '.video-embed' },
            { tag: '.audio-embed' },
          ],
          includeTags: [],
        },
        // Processing options for English feed
        processingOptions: {
          maxFirecrawlScrape: 20, // Scrape up to 20 articles
          maxArticlesPerRun: 15, // Send up to 15 to OpenAI
          customTimeout: 45, // 45 seconds
          enableStealth: false,
          skipTranslation: false, // English content still gets company extraction
          skipEnhancement: false,
        },
        // Keyword filtering options
        keywordFiltering: {
          strictKeywordMatching: false,
          customKeywords: [],
        },
      },
      {
        name: 'Handelsblatt - Schlagzeilen',
        url: 'https://www.handelsblatt.com/contentexport/feed/schlagzeilen',
        isActive: true,
        language: 'de' as const,
        processingFrequency: 360, // 6 hours
        priority: 'high' as const,
        // Enhanced Firecrawl options with site-specific settings
        firecrawlOptions: {
          removeBase64Images: true,
          blockAds: true,
          excludeTags: [
            ...commonExcludeTags,
            { tag: '.premium' },
            { tag: '.abo' },
            { tag: '.subscription-wall' },
            { tag: '.premium-content-overlay' },
            { tag: '.premium-barrier' },
            { tag: '.paywall-prompt' },
            { tag: '.author-bio' },
            { tag: '.author-info' },
            { tag: '.byline' },
            { tag: '.tags' },
            { tag: '.tag-list' },
          ],
          includeTags: [],
        },
        // Processing options with stealth mode for Handelsblatt
        processingOptions: {
          maxFirecrawlScrape: 12, // Scrape up to 12 articles
          maxArticlesPerRun: 8, // Send up to 8 to OpenAI
          customTimeout: 60, // 60 seconds
          enableStealth: true, // Handelsblatt requires stealth mode
          skipTranslation: false,
          skipEnhancement: false,
        },
        // Keyword filtering options
        keywordFiltering: {
          strictKeywordMatching: false,
          customKeywords: [],
        },
      },
      {
        name: 'Stern.de - Alle Nachrichten',
        url: 'https://www.stern.de/feed/standard/alle-nachrichten/',
        isActive: true,
        language: 'de' as const,
        processingFrequency: 720, // 12 hours
        priority: 'low' as const,
        // Enhanced Firecrawl options with default settings
        firecrawlOptions: {
          removeBase64Images: true,
          blockAds: true,
          excludeTags: commonExcludeTags,
          includeTags: [],
        },
        // Processing options
        processingOptions: {
          maxFirecrawlScrape: 15, // Scrape up to 15 articles
          maxArticlesPerRun: 10, // Send up to 10 to OpenAI
          customTimeout: 30, // 30 seconds
          enableStealth: false,
          skipTranslation: false,
          skipEnhancement: false,
        },
        // Keyword filtering options
        keywordFiltering: {
          strictKeywordMatching: false,
          customKeywords: [],
        },
      },
    ];

    for (const feedData of rssFeeds) {
      try {
        const existing = await payload.find({
          collection: 'rss-feeds',
          where: { url: { equals: feedData.url } },
          limit: 1,
        });

        if (existing.docs.length === 0) {
          await payload.create({
            collection: 'rss-feeds',
            data: feedData,
          });
          feedsCreated++;
          console.log(`✅ Created RSS feed: ${feedData.name}`);
          console.log(`   📋 Settings applied:`);
          console.log(`      - Language: ${feedData.language.toUpperCase()}`);
          console.log(`      - Priority: ${feedData.priority}`);
          console.log(
            `      - Max Firecrawl scrape: ${feedData.processingOptions.maxFirecrawlScrape}`
          );
          console.log(
            `      - Max articles per run: ${feedData.processingOptions.maxArticlesPerRun}`
          );
          console.log(
            `      - Custom timeout: ${feedData.processingOptions.customTimeout}s`
          );
          console.log(
            `      - Stealth mode: ${feedData.processingOptions.enableStealth ? 'enabled' : 'disabled'}`
          );
          console.log(
            `      - Exclude tags: ${feedData.firecrawlOptions.excludeTags.length} configured`
          );
          console.log(
            `      - Remove Base64 images: ${feedData.firecrawlOptions.removeBase64Images ? 'enabled' : 'disabled'}`
          );
          console.log(
            `      - Block ads: ${feedData.firecrawlOptions.blockAds ? 'enabled' : 'disabled'}`
          );
        } else {
          console.log(`⚠️ RSS feed exists: ${feedData.name}`);
        }
      } catch (error: any) {
        console.error(
          `❌ Failed to create RSS feed ${feedData.name}:`,
          error.message
        );
      }
    }

    // Step 4: Get final database stats
    const [keywordStats, categoryStats, feedStats, articleStats] =
      await Promise.all([
        payload.find({ collection: 'keywords', limit: 1000 }),
        payload.find({ collection: 'categories', limit: 1000 }),
        payload.find({ collection: 'rss-feeds', limit: 1000 }),
        payload.find({ collection: 'articles', limit: 1000 }),
      ]);

    const processingTime = Date.now() - startTime;

    const response = {
      success: true,
      message: 'Content loading completed successfully',
      timing: {
        processingTimeMs: processingTime,
        processingTimeSeconds: Math.round(processingTime / 1000),
        startTime: new Date(startTime).toISOString(),
        endTime: new Date().toISOString(),
      },
      created: {
        keywords: keywordsCreated,
        categories: categoriesCreated,
        rssFeeds: feedsCreated,
      },
      totals: {
        keywords: keywordStats.totalDocs,
        categories: categoryStats.totalDocs,
        rssFeeds: feedStats.totalDocs,
        articles: articleStats.totalDocs,
      },
      nextSteps: [
        'Run RSS processing: POST /api/run-content-pipeline',
        'Check admin interface: http://localhost:3001/admin',
        'Monitor article creation in Articles collection',
      ],
      adminUrls: {
        articles: 'http://localhost:3001/admin/collections/articles',
        rssFeeds: 'http://localhost:3001/admin/collections/rss-feeds',
        keywords: 'http://localhost:3001/admin/collections/keywords',
        categories: 'http://localhost:3001/admin/collections/categories',
      },
    };

    console.log('\n📊 Content Loading Summary:');
    console.log(`Keywords created: ${keywordsCreated}/${keywords.length}`);
    console.log(
      `Categories created: ${categoriesCreated}/${categories.length}`
    );
    console.log(`RSS Feeds created: ${feedsCreated}/${rssFeeds.length}`);
    console.log(`Processing time: ${Math.round(processingTime / 1000)}s`);

    return NextResponse.json(response);
  } catch (error: any) {
    const processingTime = Date.now() - startTime;
    console.error('❌ Content loading failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: error.message,
        message: 'Content loading failed',
        timing: {
          processingTimeMs: processingTime,
          processingTimeSeconds: Math.round(processingTime / 1000),
          startTime: new Date(startTime).toISOString(),
          endTime: new Date().toISOString(),
        },
      },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint for current database state
 */
export async function GET() {
  try {
    const payload = await getPayload({ config });

    const [keywordStats, categoryStats, feedStats, articleStats] =
      await Promise.all([
        payload.find({ collection: 'keywords', limit: 1000 }),
        payload.find({ collection: 'categories', limit: 1000 }),
        payload.find({ collection: 'rss-feeds', limit: 1000 }),
        payload.find({ collection: 'articles', limit: 1000 }),
      ]);

    return NextResponse.json({
      success: true,
      message: 'Current database state',
      totals: {
        keywords: keywordStats.totalDocs,
        categories: categoryStats.totalDocs,
        rssFeeds: feedStats.totalDocs,
        articles: articleStats.totalDocs,
      },
      readyForRssProcessing:
        feedStats.totalDocs > 0 && keywordStats.totalDocs > 0,
      adminUrls: {
        articles: 'http://localhost:3001/admin/collections/articles',
        rssFeeds: 'http://localhost:3001/admin/collections/rss-feeds',
        keywords: 'http://localhost:3001/admin/collections/keywords',
        categories: 'http://localhost:3001/admin/collections/categories',
      },
    });
  } catch (error: any) {
    return NextResponse.json(
      {
        success: false,
        error: error.message,
        message: 'Failed to get database state',
      },
      { status: 500 }
    );
  }
}
