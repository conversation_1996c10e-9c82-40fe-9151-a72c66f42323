import config from '@payload-config';
import { NextResponse } from 'next/server';
import { getPayload } from 'payload';
import { getFirecrawlStats } from '@/lib/integrations/firecrawl/enhanced-client';
import { rssProcessingService } from '@/utilities/RSSProcessingService';

/**
 * Production Content Pipeline API (Force Mode)
 *
 * Same as production pipeline but bypasses frequency checks - useful for testing
 * This endpoint processes ALL active feeds regardless of their processingFrequency setting
 */
export async function POST() {
  const startTime = Date.now();

  try {
    console.log(
      '🚀 Starting FORCED production content pipeline (bypassing frequency checks)...'
    );
    console.log(`⏰ Start time: ${new Date(startTime).toISOString()}`);

    const payload = await getPayload({ config });

    // Step 1: Validate database state
    console.log('🔍 Validating database state...');

    const [rssFeeds, keywords] = await Promise.all([
      payload.find({
        collection: 'rss-feeds',
        where: { isActive: { equals: true } },
        limit: 100,
      }),
      payload.find({
        collection: 'keywords' as any,
        where: { isActive: { equals: true } },
        limit: 100,
      }),
    ]);

    if (rssFeeds.totalDocs === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'No active RSS feeds found',
        },
        { status: 400 }
      );
    }

    if (keywords.totalDocs === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'No keywords found',
        },
        { status: 400 }
      );
    }

    console.log(`✅ Database validation passed:`);
    console.log(`   - Active RSS feeds: ${rssFeeds.totalDocs}`);
    console.log(`   - Active keywords: ${keywords.totalDocs}`);

    // Step 2: Execute RSS processing pipeline (FORCE MODE)
    console.log('\n📡 Executing RSS processing pipeline (FORCE MODE)...');
    console.log('⚠️  Processing ALL feeds regardless of frequency settings');

    const processingResult = await rssProcessingService.processAllFeedsForce();

    // Step 3: Calculate processing time
    const processingTime = Date.now() - startTime;
    const processingTimeSeconds = Math.round(processingTime / 1000);

    // Step 4: Get updated article counts
    const articleStats = await Promise.all([
      payload.find({
        collection: 'articles',
        where: { workflowStage: { equals: 'candidate-article' } },
        limit: 1000,
      }),
      payload.find({
        collection: 'articles',
        where: { _status: { equals: 'published' } },
        limit: 1000,
      }),
      payload.find({ collection: 'articles', limit: 1000 }),
    ]);

    // Step 5: Get Firecrawl API usage stats
    const firecrawlStats = getFirecrawlStats();

    // Step 6: Prepare response
    const response = {
      success: processingResult.success,
      mode: 'FORCE',
      message: 'Production pipeline completed (frequency checks bypassed)',
      processingTime: {
        milliseconds: processingTime,
        seconds: processingTimeSeconds,
        minutes: Math.round(processingTimeSeconds / 60),
      },
      results: {
        totalProcessed: processingResult.processed,
        totalAccepted: processingResult.accepted,
        totalRejected: processingResult.rejected,
        errors: processingResult.errors,
        successRate:
          processingResult.processed > 0
            ? Math.round(
                (processingResult.accepted / processingResult.processed) * 100
              )
            : 0,
      },
      articleCounts: {
        candidateArticles: articleStats[0].totalDocs,
        publishedArticles: articleStats[1].totalDocs,
        totalArticles: articleStats[2].totalDocs,
      },
      firecrawlUsage: {
        totalRequests: firecrawlStats.totalRequests,
        successfulRequests: firecrawlStats.successfulRequests,
        failedRequests: firecrawlStats.failedRequests,
        rateLimitErrors: firecrawlStats.rateLimitErrors,
        configErrors: firecrawlStats.configErrors,
        timeoutErrors: firecrawlStats.timeoutErrors,
        authErrors: firecrawlStats.authErrors,
        successRate: firecrawlStats.successRate,
      },
      timestamp: new Date().toISOString(),
    };

    console.log('\n🎯 Final Summary:');
    console.log(`   ⏱️  Processing time: ${processingTimeSeconds}s`);
    console.log(
      `   📊 Results: ${processingResult.accepted} accepted, ${processingResult.rejected} rejected`
    );
    console.log(
      `   🔥 Firecrawl: ${firecrawlStats.totalRequests} requests, ${firecrawlStats.successRate}% success`
    );
    console.log(
      `   📄 Articles: ${articleStats[2].totalDocs} total, ${articleStats[0].totalDocs} candidates`
    );

    return NextResponse.json(response);
  } catch (error: any) {
    console.error('❌ Production pipeline failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: error.message,
        processingTime: Date.now() - startTime,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
