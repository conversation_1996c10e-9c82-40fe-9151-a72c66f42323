import { getPayload } from 'payload';
import config from '@payload-config';

/**
 * Email Reporting Service for Content Pipeline
 *
 * Provides comprehensive email notifications for content pipeline operations
 * including processing summaries, article links, and actionable insights.
 */

export interface PipelineResult {
  success: boolean;
  processed: number;
  accepted: number;
  rejected: number;
  errors: string[];
  details: Array<{
    url: string;
    title: string;
    status: 'accepted' | 'rejected' | 'error' | 'rate_limited';
    reason?: string;
    articleId?: string;
  }>;
  timing: {
    processingTimeMs: number;
    processingTimeSeconds: number;
    startTime: string;
    endTime: string;
  };
  database: {
    totalArticles: number;
    candidateArticles: number;
    publishedArticles: number;
  };
}

export interface ArticleSummary {
  id: string;
  title: string;
  status: string; // Use native PayloadCMS status instead of editorial workflowStage
  createdAt: string;
  sourceUrl?: string;
  adminUrl: string;
}

/**
 * Send comprehensive pipeline completion email
 */
export async function sendPipelineReport(
  pipelineType:
    | 'test-content-pipeline'
    | 'production-pipeline'
    | 'single-pipeline',
  result: PipelineResult,
  baseUrl: string = 'http://localhost:3001'
): Promise<void> {
  try {
    console.log('📧 Generating pipeline email report...');

    const payload = await getPayload({ config });

    // Get draft articles (using native PayloadCMS status, not editorial workflow)
    const draftArticles = await payload.find({
      collection: 'articles',
      where: { _status: { equals: 'draft' } },
      limit: 50,
      sort: '-createdAt',
    });

    // Get recently accepted articles from this run
    const acceptedIds = result.details
      .filter(d => d.status === 'accepted' && d.articleId)
      .map(d => d.articleId!);

    let newArticles: ArticleSummary[] = [];
    if (acceptedIds.length > 0) {
      const newArticleResults = await payload.find({
        collection: 'articles',
        where: {
          id: { in: acceptedIds },
        },
        limit: 50,
      });

      newArticles = newArticleResults.docs.map((article: any) => ({
        id: article.id.toString(),
        title: article.title || 'Untitled Article',
        status: article._status || 'unknown', // Use native PayloadCMS status
        createdAt: article.createdAt,
        sourceUrl: article.sourceUrl,
        adminUrl: `${baseUrl}/admin/collections/articles/${article.id}`,
      }));
    }

    // Generate email content
    const emailSubject = generateEmailSubject(pipelineType, result);
    const emailContent = generateEmailContent(
      pipelineType,
      result,
      newArticles,
      draftArticles.docs.map((article: any) => ({
        id: article.id.toString(),
        title: article.title || 'Untitled Article',
        status: article._status || 'unknown', // Use native PayloadCMS status instead of workflowStage
        createdAt: article.createdAt,
        sourceUrl: article.sourceUrl,
        adminUrl: `${baseUrl}/admin/collections/articles/${article.id}`,
      })),
      baseUrl
    );

    // Send email using PayloadCMS email system
    // Support multiple email addresses (comma-separated)
    const recipients =
      process.env.PIPELINE_NOTIFICATION_EMAIL ||
      process.env.RESEND_DEFAULT_FROM_ADDRESS ||
      '<EMAIL>';

    const emailAddresses = recipients
      .split(',')
      .map(email => email.trim())
      .filter(email => email);

    await payload.sendEmail({
      to: emailAddresses,
      subject: emailSubject,
      html: emailContent,
    });

    console.log('✅ Pipeline email report sent successfully');
  } catch (error) {
    console.error('❌ Failed to send pipeline email report:', error);
    // Don't throw - email failure shouldn't break the pipeline
  }
}

/**
 * Generate email subject based on pipeline results
 */
function generateEmailSubject(
  pipelineType: string,
  result: PipelineResult
): string {
  const pipelineName = pipelineType
    .replace('-', ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
  const status = result.success ? '✅ Completed' : '❌ Failed';
  const summary = result.success
    ? `${result.accepted} articles processed`
    : `${result.errors.length} errors`;

  return `${status}: ${pipelineName} - ${summary}`;
}

/**
 * Generate comprehensive HTML email content
 */
function generateEmailContent(
  pipelineType: string,
  result: PipelineResult,
  newArticles: ArticleSummary[],
  candidateArticles: ArticleSummary[],
  baseUrl: string
): string {
  const pipelineName = pipelineType
    .replace('-', ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
  const statusIcon = result.success ? '✅' : '❌';
  const statusColor = result.success ? '#059669' : '#DC2626';

  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Börsen Blick - Pipeline Report</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            line-height: 1.6; 
            color: #374151; 
            background-color: #f9fafb; 
            margin: 0; 
            padding: 0; 
        }
        .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 8px; 
            box-shadow: 0 1px 3px rgba(0,0,0,0.1); 
            overflow: hidden; 
        }
        .header { 
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); 
            color: white; 
            padding: 24px; 
            text-align: center; 
        }
        .header h1 { 
            margin: 0; 
            font-size: 24px; 
            font-weight: 600; 
        }
        .content { 
            padding: 24px; 
        }
        .status-badge { 
            display: inline-block; 
            padding: 8px 16px; 
            border-radius: 20px; 
            font-weight: 600; 
            font-size: 14px; 
            color: white; 
            background-color: ${statusColor}; 
            margin-bottom: 20px; 
        }
        .metric-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); 
            gap: 16px; 
            margin: 24px 0; 
        }
        .metric { 
            text-align: center; 
            padding: 16px; 
            background: #f3f4f6; 
            border-radius: 8px; 
        }
        .metric-value { 
            font-size: 24px; 
            font-weight: 700; 
            color: #1f2937; 
        }
        .metric-label { 
            font-size: 12px; 
            text-transform: uppercase; 
            font-weight: 600; 
            color: #6b7280; 
            margin-top: 4px; 
        }
        .section { 
            margin: 32px 0; 
        }
        .section h2 { 
            font-size: 18px; 
            font-weight: 600; 
            margin-bottom: 16px; 
            color: #1f2937; 
            border-bottom: 2px solid #e5e7eb; 
            padding-bottom: 8px; 
        }
        .article-list { 
            list-style: none; 
            padding: 0; 
            margin: 0; 
        }
        .article-item { 
            background: #fafafa; 
            border-left: 4px solid #3b82f6; 
            margin: 12px 0; 
            padding: 16px; 
            border-radius: 4px; 
        }
        .article-title { 
            font-weight: 600; 
            color: #1f2937; 
            margin-bottom: 8px; 
        }
        .article-meta { 
            font-size: 14px; 
            color: #6b7280; 
            margin-bottom: 8px; 
        }
        .article-link { 
            display: inline-block; 
            background: #3b82f6; 
            color: white; 
            padding: 6px 12px; 
            border-radius: 4px; 
            text-decoration: none; 
            font-size: 14px; 
            font-weight: 500; 
        }
        .error-list { 
            background: #fef2f2; 
            border: 1px solid #fecaca; 
            border-radius: 4px; 
            padding: 16px; 
        }
        .error-item { 
            color: #dc2626; 
            margin: 8px 0; 
            font-family: monospace; 
            font-size: 14px; 
        }
        .footer { 
            background: #f3f4f6; 
            padding: 24px; 
            text-align: center; 
            color: #6b7280; 
            font-size: 14px; 
        }
        .quick-links { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 12px; 
            margin: 24px 0; 
        }
        .quick-link { 
            display: block; 
            background: #3b82f6; 
            color: white; 
            padding: 12px 16px; 
            border-radius: 6px; 
            text-decoration: none; 
            text-align: center; 
            font-weight: 500; 
        }
        .timing { 
            background: #f0f9ff; 
            border: 1px solid #bfdbfe; 
            border-radius: 4px; 
            padding: 16px; 
            margin: 16px 0; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${statusIcon} Börsen Blick</h1>
            <p>${pipelineName} Report</p>
        </div>
        
        <div class="content">
            <div class="status-badge">
                ${result.success ? 'Successfully Completed' : 'Failed'}
            </div>
            
            <div class="metric-grid">
                <div class="metric">
                    <div class="metric-value">${result.processed}</div>
                    <div class="metric-label">Processed</div>
                </div>
                <div class="metric">
                    <div class="metric-value">${result.accepted}</div>
                    <div class="metric-label">Accepted</div>
                </div>
                <div class="metric">
                    <div class="metric-value">${result.rejected}</div>
                    <div class="metric-label">Rejected</div>
                </div>
                <div class="metric">
                    <div class="metric-value">${result.errors.length}</div>
                    <div class="metric-label">Errors</div>
                </div>
            </div>

            <div class="timing">
                <strong>Processing Time:</strong> ${result.timing.processingTimeSeconds}s<br>
                <strong>Started:</strong> ${new Date(result.timing.startTime).toLocaleString()}<br>
                <strong>Completed:</strong> ${new Date(result.timing.endTime).toLocaleString()}
            </div>

            <div class="quick-links">
                <a href="${baseUrl}/admin/collections/articles" class="quick-link">
                    📰 All Articles (${result.database.totalArticles})
                </a>
                <a href="${baseUrl}/admin/collections/articles?where%5BworkflowStage%5D%5Bequals%5D=candidate-article" class="quick-link">
                    🔍 Review Queue (${result.database.candidateArticles})
                </a>
                <a href="${baseUrl}/admin/collections/articles?where%5B_status%5D%5Bequals%5D=published" class="quick-link">
                    ✅ Published (${result.database.publishedArticles})
                </a>
            </div>

            ${
              newArticles.length > 0
                ? `
            <div class="section">
                <h2>🆕 Newly Created Articles (${newArticles.length})</h2>
                <ul class="article-list">
                    ${newArticles
                      .map(
                        article => `
                    <li class="article-item">
                        <div class="article-title">${article.title}</div>
                        <div class="article-meta">
                            Created: ${new Date(article.createdAt).toLocaleString()} | 
                            Status: ${article.status}
                            ${article.sourceUrl ? ` | <a href="${article.sourceUrl}" target="_blank">Source</a>` : ''}
                        </div>
                        <a href="${article.adminUrl}" class="article-link">Review Article →</a>
                    </li>
                    `
                      )
                      .join('')}
                </ul>
            </div>
            `
                : ''
            }

            ${
              candidateArticles.length > 0
                ? `
            <div class="section">
                <h2>📋 Articles Awaiting Review (${candidateArticles.length})</h2>
                <p>These articles are ready for editorial review and publication:</p>
                <ul class="article-list">
                    ${candidateArticles
                      .slice(0, 10)
                      .map(
                        article => `
                    <li class="article-item">
                        <div class="article-title">${article.title}</div>
                        <div class="article-meta">
                            Created: ${new Date(article.createdAt).toLocaleString()}
                            ${article.sourceUrl ? ` | <a href="${article.sourceUrl}" target="_blank">Source</a>` : ''}
                        </div>
                        <a href="${article.adminUrl}" class="article-link">Review Article →</a>
                    </li>
                    `
                      )
                      .join('')}
                </ul>
                ${candidateArticles.length > 10 ? `<p><strong>...and ${candidateArticles.length - 10} more</strong> <a href="${baseUrl}/admin/collections/articles?where%5BworkflowStage%5D%5Bequals%5D=candidate-article">View all →</a></p>` : ''}
            </div>
            `
                : ''
            }

            ${
              result.errors.length > 0
                ? `
            <div class="section">
                <h2>⚠️ Errors (${result.errors.length})</h2>
                <div class="error-list">
                    ${result.errors.map(error => `<div class="error-item">${error}</div>`).join('')}
                </div>
            </div>
            `
                : ''
            }

            ${
              result.details.length > 0
                ? `
            <div class="section">
                <h2>📊 Processing Details</h2>
                <ul class="article-list">
                    ${result.details
                      .slice(0, 10)
                      .map(
                        detail => `
                    <li class="article-item" style="border-left-color: ${detail.status === 'accepted' ? '#059669' : detail.status === 'rejected' ? '#d97706' : '#dc2626'};">
                        <div class="article-title">${detail.title}</div>
                        <div class="article-meta">
                            Status: <strong>${detail.status.toUpperCase()}</strong>
                            ${detail.reason ? ` | ${detail.reason}` : ''}
                        </div>
                        ${detail.articleId ? `<a href="${baseUrl}/admin/collections/articles/${detail.articleId}" class="article-link">View Article →</a>` : ''}
                    </li>
                    `
                      )
                      .join('')}
                </ul>
                ${result.details.length > 10 ? `<p><strong>...and ${result.details.length - 10} more items processed</strong></p>` : ''}
            </div>
            `
                : ''
            }
        </div>
        
        <div class="footer">
            <p>Generated by Börsen Blick Content Pipeline<br>
            ${new Date().toLocaleString()}</p>
        </div>
    </div>
</body>
</html>
  `;
}

/**
 * Send error notification email
 */
export async function sendPipelineErrorEmail(
  pipelineType: string,
  error: Error,
  baseUrl: string = 'http://localhost:3001'
): Promise<void> {
  try {
    const payload = await getPayload({ config });

    // Support multiple email addresses (comma-separated)
    const recipients =
      process.env.PIPELINE_NOTIFICATION_EMAIL ||
      process.env.RESEND_DEFAULT_FROM_ADDRESS ||
      '<EMAIL>';

    const emailAddresses = recipients
      .split(',')
      .map(email => email.trim())
      .filter(email => email);

    await payload.sendEmail({
      to: emailAddresses,
      subject: `❌ Pipeline Failed: ${pipelineType}`,
      html: `
        <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: #dc2626; color: white; padding: 20px; border-radius: 8px; text-align: center;">
            <h1>❌ Pipeline Failed</h1>
            <p>${pipelineType.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
          </div>
          
          <div style="padding: 20px; background: #fef2f2; border: 1px solid #fecaca; border-radius: 4px; margin: 20px 0;">
            <h3>Error Details:</h3>
            <pre style="background: white; padding: 16px; border-radius: 4px; overflow-x: auto;">${error.message}</pre>
            
            ${
              error.stack
                ? `
            <h3>Stack Trace:</h3>
            <pre style="background: white; padding: 16px; border-radius: 4px; overflow-x: auto; font-size: 12px;">${error.stack}</pre>
            `
                : ''
            }
          </div>
          
          <div style="text-align: center; margin: 20px 0;">
            <a href="${baseUrl}/admin" style="background: #3b82f6; color: white; padding: 12px 24px; border-radius: 6px; text-decoration: none;">
              Check Admin Panel →
            </a>
          </div>
          
          <div style="text-align: center; color: #6b7280; font-size: 14px;">
            Generated by Börsen Blick Content Pipeline<br>
            ${new Date().toLocaleString()}
          </div>
        </div>
      `,
    });

    console.log('✅ Pipeline error email sent');
  } catch (emailError) {
    console.error('❌ Failed to send pipeline error email:', emailError);
  }
}
